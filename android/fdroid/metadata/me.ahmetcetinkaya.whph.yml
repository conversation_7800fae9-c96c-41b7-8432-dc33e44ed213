Categories:
  - Science & Education
License: GPL-3.0-or-later
AuthorName: Ahmet Çetinkaya
AuthorEmail: <EMAIL>
WebSite: https://whph.ahmetcetinkaya.me
SourceCode: https://github.com/ahmet-cetinkaya/whph
IssueTracker: https://github.com/ahmet-cetinkaya/whph/issues
Donate: https://ahmetcetinkaya.me/donate

AutoName: Work Hard Play Hard

RepoType: git
Repo: https://github.com/ahmet-cetinkaya/whph

Builds:
  - versionName: 0.9.10
    versionCode: 47
    commit: 1ed2569a8378f3a4a81aaf94889ea083670d443d
    submodules: true
    output: build/app/outputs/flutter-apk/app-x86_64-release.apk
    srclibs:
      - flutter@stable
    rm:
      - ios
      - linux
      - macos
      - web
      - windows
    prebuild:
      - flutterVersion=$(bash scripts/get_flutter_version.sh)
      - '[[ $flutterVersion ]] || exit 1'
      - git -C $$flutter$$ checkout -f $flutterVersion
      - export PUB_CACHE=$(pwd)/.pub-cache
      - $$flutter$$/bin/flutter config --no-analytics
      - $$flutter$$/bin/flutter packages pub get
    scandelete:
      - android
      - .pub-cache
    build:
      - export PUB_CACHE=$(pwd)/.pub-cache
      - export VERSION_CODE=47
      - $$flutter$$/bin/flutter build apk --release --split-per-abi --target-platform="android-x64"
        --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons

  - versionName: 0.9.10
    versionCode: 48
    commit: 1ed2569a8378f3a4a81aaf94889ea083670d443d
    submodules: true
    output: build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk
    srclibs:
      - flutter@stable
    rm:
      - ios
      - linux
      - macos
      - web
      - windows
    prebuild:
      - flutterVersion=$(bash scripts/get_flutter_version.sh)
      - '[[ $flutterVersion ]] || exit 1'
      - git -C $$flutter$$ checkout -f $flutterVersion
      - export PUB_CACHE=$(pwd)/.pub-cache
      - $$flutter$$/bin/flutter config --no-analytics
      - $$flutter$$/bin/flutter packages pub get
    scandelete:
      - android
      - .pub-cache
    build:
      - export PUB_CACHE=$(pwd)/.pub-cache
      - export VERSION_CODE=48
      - $$flutter$$/bin/flutter build apk --release --split-per-abi --target-platform="android-arm"
        --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons

  - versionName: 0.9.10
    versionCode: 49
    commit: 1ed2569a8378f3a4a81aaf94889ea083670d443d
    submodules: true
    output: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
    srclibs:
      - flutter@stable
    rm:
      - ios
      - linux
      - macos
      - web
      - windows
    prebuild:
      - flutterVersion=$(bash scripts/get_flutter_version.sh)
      - '[[ $flutterVersion ]] || exit 1'
      - git -C $$flutter$$ checkout -f $flutterVersion
      - export PUB_CACHE=$(pwd)/.pub-cache
      - $$flutter$$/bin/flutter config --no-analytics
      - $$flutter$$/bin/flutter packages pub get
    scandelete:
      - android
      - .pub-cache
    build:
      - export PUB_CACHE=$(pwd)/.pub-cache
      - export VERSION_CODE=49
      - $$flutter$$/bin/flutter build apk --release --split-per-abi --target-platform="android-arm64"
        --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons

AllowedAPKSigningKeys: 4b0de165375bb1179fbee37fbd70de03813284529e0b0c5d3ce5e794f03aa0ae

AutoUpdateMode: Version
UpdateCheckMode: Tags
UpdateCheckData: pubspec.yaml|version:\s.+\+(\d+)|.|version:\s(.+)\+
CurrentVersion: 0.9.10+47
CurrentVersionCode: 47
