# App Usage Plugin
-keep class dk.cachet.app_usage.** { *; }
-keepclassmembers class dk.cachet.app_usage.** { *; }

# Aggressive Google services removal for F-Droid compatibility
# Remove ALL Google Play Services and Google Play Core classes
-assumenosideeffects class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**

# Remove ALL Google Play Core classes
-assumenosideeffects class com.google.android.play.core.** { *; }
-dontwarn com.google.android.play.core.**

# Remove ALL Google classes (comprehensive removal)
-assumenosideeffects class com.google.** { *; }
-dontwarn com.google.**

# Additional specific exclusions for problematic classes (redundant but explicit)
# These are already covered by com.google.** but kept for clarity
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallSessionState { *; }
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallManager { *; }
-assumenosideeffects class com.google.android.play.core.tasks.OnFailureListener { *; }
-assumenosideeffects class com.google.android.play.core.splitcompat.SplitCompatApplication { *; }
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener { *; }
-assumenosideeffects class com.google.android.play.core.tasks.OnSuccessListener { *; }

# Comprehensive warning suppression for all Google packages
-dontwarn com.google.android.play.**
-dontwarn com.google.android.gms.**
-dontwarn com.google.firebase.**
-dontwarn com.google.common.**
-dontwarn com.google.gson.**
-dontwarn com.google.protobuf.**

# Flutter optimizations
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Aggressive optimizations
-allowaccessmodification
-mergeinterfacesaggressively
-optimizationpasses 5
-overloadaggressively
-repackageclasses ''

# Remove unused code
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Dart/Flutter specific optimizations
-dontwarn io.flutter.embedding.**
-dontwarn io.flutter.plugin.**

# Database optimizations
-keep class org.sqlite.** { *; }
-keep class androidx.sqlite.** { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Remove debug information
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable
